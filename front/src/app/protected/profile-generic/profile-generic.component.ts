import {Component, OnInit} from '@angular/core';
import {WorkspaceService} from '../../services/workspace/workspace.service';
import {ProfileComponent} from '../profile/profile/profile.component';
import {RecruiterProfileComponent} from '../recruiter-profile/recruiter-profile.component';
import {NgIf} from '@angular/common';
import {ActivatedRoute} from '@angular/router';
import {UserProfileService} from '../../services/user-profile/user-profile.service';

@Component({
  selector: 'app-profile-generic',
  imports: [
    ProfileComponent,
    RecruiterProfileComponent,
    NgIf
  ],
  templateUrl: './profile-generic.component.html',
  standalone: true,
  styleUrl: './profile-generic.component.css'
})
export class ProfileGenericComponent implements OnInit{
  currentWorkspaceId: string = '';
  targetUserId: string | null = null;
  isViewingOtherProfile: boolean = false;

  constructor(
    private workspaceService: WorkspaceService,
    private route: ActivatedRoute,
    private userProfileService: UserProfileService
  ) {
  }

  ngOnInit(): void {
    this.currentWorkspaceId = this.workspaceService.getCurrentWorkspaceId();

    // Vérifier si nous affichons le profil d'un autre utilisateur
    this.route.params.subscribe(params => {
      this.targetUserId = params['userId'];
      this.isViewingOtherProfile = !!this.targetUserId;

      if (this.isViewingOtherProfile) {
        console.log('👤 Affichage du profil de l\'utilisateur:', this.targetUserId);
      }
    });
  }

}
