/* Global container - Enterprise Professional Style */
.dashboard {
  display: grid;
  grid-template-columns: 250px 1fr;
  grid-template-areas: "sidebar main";
  min-height: 100vh;
  background:
    linear-gradient(135deg, #fafbfc 0%, #f4f6f8 25%, #eef2f6 50%, #f4f6f8 75%, #fafbfc 100%),
    radial-gradient(ellipse at 20% 30%, rgba(255, 107, 53, 0.02) 0%, transparent 70%),
    radial-gradient(ellipse at 80% 70%, rgba(59, 130, 246, 0.015) 0%, transparent 70%);
  font-family: 'Inter', 'SF Pro Display', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
  overflow-x: hidden;
  position: relative;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

.dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(0deg, transparent 24px, rgba(0, 0, 0, 0.02) 25px, rgba(0, 0, 0, 0.02) 26px, transparent 27px, transparent 49px, rgba(0, 0, 0, 0.02) 50px, rgba(0, 0, 0, 0.02) 51px, transparent 52px),
    linear-gradient(90deg, transparent 24px, rgba(0, 0, 0, 0.02) 25px, rgba(0, 0, 0, 0.02) 26px, transparent 27px, transparent 49px, rgba(0, 0, 0, 0.02) 50px, rgba(0, 0, 0, 0.02) 51px, transparent 52px);
  pointer-events: none;
  z-index: 0;
}

/* Sidebar */
app-sidebar-recruiter {
  grid-area: sidebar;
  z-index: 100;
  position: relative;
}

/* Main content - Enterprise Panel */
.dashboard-main {
  grid-area: main;
  display: flex;
  flex-direction: column;
  background:
    linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(250, 251, 252, 0.9) 100%);
  backdrop-filter: blur(24px) saturate(110%);
  border-radius: 24px 0 0 24px;
  margin: 12px 0 12px 12px;
  position: relative;
  z-index: 1;
  overflow-y: auto;
  max-height: calc(100vh - 24px);
  border: 1px solid rgba(0, 0, 0, 0.06);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* Header - Enterprise Command Center */
.dashboard-header {
  background:
    linear-gradient(180deg, rgba(255, 255, 255, 0.98) 0%, rgba(250, 251, 252, 0.95) 100%);
  backdrop-filter: blur(24px) saturate(105%);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  padding: 28px 36px;
  position: sticky;
  top: 0;
  z-index: 50;
  border-radius: 24px 0 0 0;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Title - Enterprise Branding */
.dashboard-title {
  display: flex;
  align-items: center;
  gap: 14px;
  font-size: 28px;
  font-weight: 700;
  color: #0f172a;
  margin: 0;
  letter-spacing: -0.6px;
  position: relative;
  font-feature-settings: 'ss01', 'ss02';
}

.dashboard-title::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 48px;
  height: 2px;
  background: linear-gradient(90deg, #FF6B35, #3b82f6);
  border-radius: 1px;
  opacity: 0.8;
}

.title-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: #FF6B35;
  opacity: 0.9;
}

.dashboard-subtitle {
  color: #475569;
  font-size: 12px;
  font-weight: 500;
  margin: 0;
  letter-spacing: 0.8px;
  text-transform: uppercase;
  opacity: 0.8;
}

/* Search container - Enterprise Search */
.search-container {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-radius: 10px;
  padding: 10px 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 260px;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.search-container:hover,
.search-container:focus-within {
  border-color: rgba(59, 130, 246, 0.4);
  background: rgba(255, 255, 255, 0.95);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.search-icon {
  color: #FF6B35;
  font-size: 20px;
  margin-right: 8px;
  transition: all 0.3s ease;
}

.search-bar {
  border: none;
  background: transparent;
  padding: 0;
  width: 220px;
  font-size: 14px;
  color: #1e293b;
  outline: none;
  font-weight: 400;
}

.search-bar::placeholder {
  color: #94a3b8;
}

/* Profile button - Enterprise Access */
.profile-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  color: #ffffff !important;
  border-radius: 8px;
  padding: 10px 16px;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.6px;
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.profile-button:hover {
  background: linear-gradient(135deg, #FF6B35, #ea580c);
  box-shadow:
    0 6px 16px rgba(255, 107, 53, 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-1px) scale(1.02);
}

.profile-button mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #ffffff;
}

/* Header Buttons - Executive Controls */
.notification-button,
.refresh-button,
.export-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: rgba(40, 40, 40, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s ease;
}

.notification-button:hover,
.refresh-button:hover,
.export-button:hover {
  background: rgba(255, 107, 53, 0.2);
  border-color: rgba(255, 107, 53, 0.4);
  color: #ffffff;
}

.notification-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  background: #FF6B35;
  color: #ffffff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.spinning {
  animation: spin 1s linear infinite;
}

/* Notifications Panel */
.notifications-panel {
  position: absolute;
  top: 100px;
  right: 32px;
  width: 400px;
  max-height: 500px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  overflow: hidden;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.notifications-header h3 {
  color: #ffffff;
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.notifications-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 16px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px;
  margin-bottom: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.notification-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.notification-item.success {
  border-left-color: #22c55e;
}

.notification-item.info {
  border-left-color: #3b82f6;
}

.notification-item.warning {
  border-left-color: #f59e0b;
}

.notification-item.error {
  border-left-color: #ef4444;
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  color: #ffffff;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.notification-content p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 13px;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.notification-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 11px;
}

/* Card container */
.card-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 32px;
}

/* Statistics Overview */
.stats-overview {
  padding: 32px;
  margin-bottom: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stat-card {
  background:
    linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(250, 251, 252, 0.9) 100%);
  backdrop-filter: blur(24px) saturate(105%);
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 20px;
  padding: 32px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 1px 0 rgba(255, 255, 255, 0.9),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-left: 3px solid transparent;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background: linear-gradient(180deg, #FF6B35, #3b82f6);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.stat-card:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.12),
    0 1px 0 rgba(255, 255, 255, 0.95),
    inset 0 1px 0 rgba(255, 255, 255, 0.95);
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-content {
  position: relative;
  z-index: 1;
}

.stat-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.stat-header mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: #3b82f6;
  opacity: 0.8;
}

.stat-label {
  color: #475569;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1.2px;
  opacity: 0.9;
}

.stat-value {
  font-size: 36px;
  font-weight: 800;
  color: #0f172a;
  margin-bottom: 12px;
  line-height: 0.9;
  font-feature-settings: 'tnum', 'lnum';
}

.stat-change {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
  display: inline-block;
}

.stat-change.positive {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.stat-change.neutral {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Quick Actions */
.quick-actions {
  padding: 32px;
  margin-bottom: 24px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: #1e293b;
  margin-bottom: 24px;
}

.section-title mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: #FF6B35;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.action-card {
  background:
    linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(250, 251, 252, 0.9) 100%);
  backdrop-filter: blur(24px) saturate(105%);
  border: 1px solid rgba(0, 0, 0, 0.06);
  border-radius: 18px;
  padding: 28px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 6px 24px rgba(0, 0, 0, 0.06),
    0 1px 0 rgba(255, 255, 255, 0.9),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.action-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.02), rgba(59, 130, 246, 0.02));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.action-card:hover {
  transform: translateY(-6px) scale(1.01);
  box-shadow:
    0 16px 48px rgba(0, 0, 0, 0.1),
    0 1px 0 rgba(255, 255, 255, 0.95),
    inset 0 1px 0 rgba(255, 255, 255, 0.95);
  border-color: rgba(59, 130, 246, 0.2);
}

.action-card:hover::after {
  opacity: 1;
}

.action-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  background: linear-gradient(135deg, #FF6B35, #ea580c);
  box-shadow:
    0 8px 24px rgba(255, 107, 53, 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 2;
}

.action-icon mat-icon {
  font-size: 28px;
  width: 28px;
  height: 28px;
  color: #ffffff;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
}

.action-content {
  flex: 1;
  z-index: 2;
  position: relative;
}

.action-content h3 {
  font-size: 17px;
  font-weight: 700;
  color: #0f172a;
  margin: 0 0 6px 0;
  letter-spacing: -0.3px;
  font-feature-settings: 'ss01';
}

.action-content p {
  font-size: 13px;
  color: #475569;
  margin: 0;
  line-height: 1.5;
  opacity: 0.9;
}

.action-arrow {
  color: #94a3b8;
  font-size: 18px;
  width: 18px;
  height: 18px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 2;
  position: relative;
}

.action-card:hover .action-arrow {
  color: #3b82f6;
  transform: translateX(8px) scale(1.1);
}

/* Recent Activity */
.recent-activity {
  padding: 32px;
  margin-bottom: 24px;
}

.activity-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.activity-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
}

.activity-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.activity-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-title mat-icon {
  font-size: 22px;
  width: 22px;
  height: 22px;
  color: #FF6B35;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(4px);
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: rgba(255, 107, 53, 0.2);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #FF6B35;
}

.activity-details {
  flex: 1;
}

.activity-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  margin: 0 0 4px 0;
}

.activity-details p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0 0 4px 0;
}

.activity-date {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
}

.activity-status {
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

.activity-status.active {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.activity-score {
  padding: 4px 12px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  animation: spin 2s linear infinite;
  margin-bottom: 16px;
}

.loading-spinner mat-icon {
  font-size: 32px;
  width: 32px;
  height: 32px;
  color: #FF6B35;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard {
    grid-template-columns: 220px 1fr;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .search-bar {
    width: 180px;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
  }

  .actions-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 16px;
  }

  .activity-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 24px;
  }
}

@media (max-width: 768px) {
  .dashboard {
    grid-template-columns: 1fr;
    grid-template-areas: "main";
  }

  app-sidebar-recruiter {
    display: none;
  }

  .dashboard-main {
    border-radius: 0;
  }

  .dashboard-header {
    padding: 16px 20px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header-left {
    text-align: center;
  }

  .dashboard-title {
    font-size: 28px;
    justify-content: center;
  }

  .header-right {
    flex-direction: column;
    gap: 12px;
  }

  .search-container {
    width: 100%;
  }

  .search-bar {
    width: 100%;
  }

  .profile-button {
    width: 100%;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-value {
    font-size: 28px;
  }

  .actions-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }

  .activity-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .activity-item {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }

  .stats-overview,
  .quick-actions,
  .recent-activity {
    padding: 20px;
  }

  .section-title {
    font-size: 20px;
    justify-content: center;
  }
}
