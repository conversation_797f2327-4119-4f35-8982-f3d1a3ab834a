<div class="results-page">
  <app-sidebar></app-sidebar>

  <div class="results-container">
    <header class="results-header">
      <h1>Test Results</h1>
      <div class="header-actions">
        <button mat-button class="refresh-button" (click)="refreshResults()" [disabled]="loading">
          <mat-icon>refresh</mat-icon> Actualiser
        </button>
        <button mat-button class="filter-button">
          <mat-icon>filter_list</mat-icon> Filtrer par
        </button>
      </div>
    </header>

    <!-- Loading indicator -->
    <div *ngIf="loading" class="loading-container">
      <mat-icon class="loading-spinner">refresh</mat-icon>
      <p>Chargement des résultats...</p>
    </div>

    <!-- Error message -->
    <div *ngIf="error && !loading" class="error-container">
      <mat-icon color="warn">error</mat-icon>
      <p>{{ error }}</p>
      <button mat-button (click)="refreshResults()">R<PERSON>sayer</button>
    </div>

    <!-- Results grid -->
    <div *ngIf="!loading && !error" class="results-grid">
      <mat-card class="result-card" *ngFor="let result of results">
        <mat-card-header class="result-header">
          <mat-card-title>{{ result.job }}</mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="candidate-info">
            <mat-icon class="avatar">person</mat-icon>
            <div>
              <p><strong>{{ result.name }}</strong></p>
              <p><mat-icon>event</mat-icon> {{ result.date }}</p>
            </div>
          </div>

          <div class="test-info">
            <div class="time-limit">
              <p><mat-icon>schedule</mat-icon> Time Limit: <span class="red-text">{{ result.timeLimit }}</span></p>
            </div>
            <p><mat-icon>timer</mat-icon> Time Passed: {{ result.timePassed }}</p>
          </div>

          <div class="score-section">
            <div class="score" [style.color]="getScoreColor(result.score)">
              <strong>Score: {{ result.score }}</strong>
              <span class="score-status">({{ getScoreStatus(result.score) }})</span>
            </div>
            <div class="score-details">
              <p><mat-icon>check_circle</mat-icon> {{ result.correctAnswers }}/{{ result.totalQuestions }} questions correctes</p>
            </div>
          </div>
        </mat-card-content>
        <div class="result-actions">
          <button mat-icon-button color="primary" title="Voir les détails">
            <mat-icon>visibility</mat-icon>
          </button>
          <button mat-icon-button color="warn" title="Supprimer">
            <mat-icon>delete</mat-icon>
          </button>
        </div>
      </mat-card>

      <!-- Empty state -->
      <div *ngIf="results.length === 0" class="empty-state">
        <mat-icon>assignment</mat-icon>
        <h3>Aucun résultat de test</h3>
        <p>Il n'y a pas encore de résultats de test à afficher.</p>
      </div>
    </div>

    <!-- Pagination -->
    <footer class="pagination-footer">
      <!-- Boutons de pagination -->
      <button mat-button class="pagination-button" (click)="prevPage()" [disabled]="currentPage === 1">
        <mat-icon>chevron_left</mat-icon>
      </button>

      <!-- Pages dynamiques -->
      <span *ngFor="let page of pages"
            (click)="goToPage(page)"
            [class.active]="currentPage === page"
            class="pagination-page">
    {{ page }}
  </span>

      <!-- Bouton suivant -->
      <button mat-button class="pagination-button" (click)="nextPage()" [disabled]="currentPage === pages.length">
        <mat-icon>chevron_right</mat-icon>
      </button>
    </footer>

  </div>
</div>
