import { TestResultWithScore, TestResultDisplay } from '../../services/test-result/test-result.service';

/**
 * Données de test mockées pour les résultats de test
 */
export const MOCK_TEST_RESULTS: TestResultWithScore[] = [
  {
    id: '1',
    testId: 'test-js-001',
    userId: 'user-001',
    candidateName: '<PERSON>b<PERSON> Saidi',
    testName: 'JavaScript Avancé',
    submittedAt: new Date('2024-01-15T10:30:00Z'),
    totalQuestions: 20,
    correctAnswers: 16,
    scorePercentage: 80
  },
  {
    id: '2',
    testId: 'test-react-001',
    userId: 'user-002',
    candidateName: 'Sanad <PERSON>',
    testName: 'React Development',
    submittedAt: new Date('2024-01-14T14:20:00Z'),
    totalQuestions: 15,
    correctAnswers: 12,
    scorePercentage: 80
  },
  {
    id: '3',
    testId: 'test-python-001',
    userId: 'user-003',
    candidateName: 'H<PERSON> ben salah',
    testName: 'Python Backend',
    submittedAt: new Date('2024-01-13T09:15:00Z'),
    totalQuestions: 25,
    correctAnswers: 15,
    scorePercentage: 60
  },
  {
    id: '4',
    testId: 'test-angular-001',
    userId: 'user-004',
    candidateName: 'Rawen Dabbebi',
    testName: 'Angular Framework',
    submittedAt: new Date('2024-01-12T16:45:00Z'),
    totalQuestions: 18,
    correctAnswers: 17,
    scorePercentage: 94
  },
  {
    id: '5',
    testId: 'test-java-001',
    userId: 'user-005',
    candidateName: 'Roua Dabbebi',
    testName: 'Java Spring Boot',
    submittedAt: new Date('2024-01-11T11:30:00Z'),
    totalQuestions: 22,
    correctAnswers: 11,
    scorePercentage: 50
  },
  {
    id: '6',
    testId: 'test-node-001',
    userId: 'user-006',
    candidateName: 'Jesser Ammar',
    testName: 'Node.js API',
    submittedAt: new Date('2024-01-10T13:20:00Z'),
    totalQuestions: 16,
    correctAnswers: 14,
    scorePercentage: 87
  }
];

/**
 * Données transformées pour l'affichage
 */
export const MOCK_DISPLAY_RESULTS: TestResultDisplay[] = [
  {
    id: '1',
    job: 'JavaScript Avancé',
    name: 'Lobna Saidi',
    date: '15/01/2024',
    timeLimit: '1h',
    timePassed: '45 min',
    score: '80%',
    correctAnswers: 16,
    totalQuestions: 20,
    userId: 'user-001'
  },
  {
    id: '2',
    job: 'React Development',
    name: 'Sanad Ammar',
    date: '14/01/2024',
    timeLimit: '1h',
    timePassed: '52 min',
    score: '80%',
    correctAnswers: 12,
    totalQuestions: 15,
    userId: 'user-002'
  },
  {
    id: '3',
    job: 'Python Backend',
    name: 'HAD ben salah',
    date: '13/01/2024',
    timeLimit: '1h30',
    timePassed: '1h15 min',
    score: '60%',
    correctAnswers: 15,
    totalQuestions: 25,
    userId: 'user-003'
  },
  {
    id: '4',
    job: 'Angular Framework',
    name: 'Rawen Dabbebi',
    date: '12/01/2024',
    timeLimit: '1h',
    timePassed: '58 min',
    score: '94%',
    correctAnswers: 17,
    totalQuestions: 18,
    userId: 'user-004'
  },
  {
    id: '5',
    job: 'Java Spring Boot',
    name: 'Roua Dabbebi',
    date: '11/01/2024',
    timeLimit: '1h30',
    timePassed: '1h20 min',
    score: '50%',
    correctAnswers: 11,
    totalQuestions: 22,
    userId: 'user-005'
  },
  {
    id: '6',
    job: 'Node.js API',
    name: 'Jesser Ammar',
    date: '10/01/2024',
    timeLimit: '1h',
    timePassed: '48 min',
    score: '87%',
    correctAnswers: 14,
    totalQuestions: 16,
    userId: 'user-006'
  }
];
