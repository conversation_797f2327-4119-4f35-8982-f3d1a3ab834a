import { Component, OnInit } from '@angular/core';
import { MatIcon } from '@angular/material/icon';
import { SidebarComponent } from '../sidebar/sidebar.component';
import { MatCard, MatCardContent, Mat<PERSON>ardHeader, MatCardTitle } from '@angular/material/card';
import { MatButton, MatIconButton } from '@angular/material/button';
import { NgForOf, NgIf, NgClass } from '@angular/common';
import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { TestResultService, TestResultDisplay } from '../../services/test-result/test-result.service';

export interface FilterOptions {
  location: string;
  scoreRange: string;
  competence: string;
  sortBy: string;
}

@Component({
  selector: 'app-testResults',
  templateUrl: './testResults.component.html',
  standalone: true,
  imports: [
    MatIcon,
    SidebarComponent,
    MatCard,
    MatCardHeader,
    MatCardContent,
    MatIconButton,
    MatButton,
    NgForOf,
    NgIf,
    NgClass,

    MatCardTitle,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatProgressSpinnerModule,
    MatSnackBarModule
  ],
  styleUrls: ['./testResults.component.css']
})
export class TestResultsComponent implements OnInit {
  // Données principales
  allResults: TestResultDisplay[] = [];
  filteredResults: TestResultDisplay[] = [];
  paginatedResults: TestResultDisplay[] = [];

  // États de l'interface
  loading = true;
  error: string | null = null;
  showFilters = false;

  // Pagination
  currentPage = 1;
  itemsPerPage = 6;
  totalPages = 0;
  pages: number[] = [];

  // Filtres
  filters: FilterOptions = {
    location: '',
    scoreRange: '',
    competence: '',
    sortBy: 'date'
  };

  // Options pour les filtres
  locationOptions = ['Toutes', 'Paris', 'Lyon', 'Marseille', 'Toulouse', 'Nice'];
  scoreRangeOptions = [
    { value: '', label: 'Tous les scores' },
    { value: '80-100', label: 'Excellent (80-100%)' },
    { value: '60-79', label: 'Bien (60-79%)' },
    { value: '40-59', label: 'Moyen (40-59%)' },
    { value: '0-39', label: 'Faible (0-39%)' }
  ];
  competenceOptions = ['Toutes', 'JavaScript', 'Python', 'Java', 'React', 'Angular', 'Node.js'];
  sortOptions = [
    { value: 'date', label: 'Date (plus récent)' },
    { value: 'score', label: 'Score (plus élevé)' },
    { value: 'name', label: 'Nom (A-Z)' }
  ];

  constructor(
    private testResultService: TestResultService,
    private snackBar: MatSnackBar,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadTestResults();
  }
  /**
   * Charge tous les résultats de test depuis la base de données
   */
  loadTestResults() {
    this.loading = true;
    this.error = null;

    console.log('🔄 Chargement des résultats depuis la base de données...');

    this.testResultService.getAllTestResults().subscribe({
      next: (data: any) => {
        console.log('📥 Données reçues du backend:', data);

        if (data && Array.isArray(data)) {
          console.log('📊 Vérification des userId dans les données brutes:');
          data.forEach((result, index) => {
            console.log(`📋 Résultat ${index + 1}:`, {
              id: result.id,
              candidateName: result.candidateName,
              userId: result.userId,
              hasUserId: !!result.userId
            });
          });

          const transformedData = this.testResultService.transformToDisplayFormat(data);
          this.allResults = transformedData;

          console.log('📊 Vérification des userId après transformation:');
          this.allResults.forEach((result, index) => {
            console.log(`🔍 Résultat transformé ${index + 1}:`, {
              id: result.id,
              name: result.name,
              userId: result.userId,
              hasUserId: !!result.userId
            });
          });

          this.applyFiltersAndPagination();
          this.loading = false;
          console.log('✅ Résultats transformés et chargés:', this.allResults);

          if (this.allResults.length === 0) {
            console.log('ℹ️ Aucun résultat de test trouvé dans la base de données');
            this.showInfoMessage('Aucun résultat de test trouvé. Passez des tests pour voir les résultats ici.');
          } else {
            this.showSuccessMessage(`${this.allResults.length} résultat(s) de test chargé(s) avec succès`);
          }
        } else {
          console.warn('⚠️ Format de données inattendu:', data);
          this.allResults = [];
          this.applyFiltersAndPagination();
          this.loading = false;
          this.showInfoMessage('Aucun résultat de test disponible');
        }
      },
      error: (error: any) => {
        console.error('❌ Erreur lors du chargement des résultats:', error);

        // Affichage d'erreur plus détaillé
        if (error.status === 401) {
          this.error = 'Erreur d\'authentification. Veuillez vous reconnecter.';
        } else if (error.status === 403) {
          this.error = 'Accès non autorisé aux résultats de test.';
        } else if (error.status === 404) {
          this.error = 'Endpoint des résultats de test non trouvé.';
        } else if (error.status === 0) {
          this.error = 'Impossible de se connecter au serveur. Vérifiez que le backend est démarré.';
        } else {
          this.error = `Erreur lors du chargement des résultats: ${error.message || 'Erreur inconnue'}`;
        }

        this.loading = false;
        this.showErrorMessage(this.error);

        // Initialiser avec un tableau vide en cas d'erreur
        this.allResults = [];
        this.applyFiltersAndPagination();
      }
    });
  }

  /**
   * Applique les filtres et met à jour la pagination
   */
  applyFiltersAndPagination() {
    // Appliquer les filtres
    this.filteredResults = this.filterResults(this.allResults);

    // Trier les résultats
    this.sortResults();

    // Calculer la pagination
    this.calculatePagination();

    // Appliquer la pagination
    this.updatePaginatedResults();
  }

  /**
   * Filtre les résultats selon les critères sélectionnés
   */
  private filterResults(results: TestResultDisplay[]): TestResultDisplay[] {
    return results.filter(result => {
      // Filtre par score
      if (this.filters.scoreRange) {
        const scoreValue = parseInt(result.score.replace('%', ''));
        const [min, max] = this.filters.scoreRange.split('-').map(Number);
        if (scoreValue < min || scoreValue > max) {
          return false;
        }
      }

      // Filtre par compétence (basé sur le nom du test)
      if (this.filters.competence && this.filters.competence !== 'Toutes') {
        if (!result.job.toLowerCase().includes(this.filters.competence.toLowerCase())) {
          return false;
        }
      }

      return true;
    });
  }

  /**
   * Trie les résultats selon le critère sélectionné
   */
  private sortResults() {
    this.filteredResults.sort((a, b) => {
      switch (this.filters.sortBy) {
        case 'score':
          const scoreA = parseInt(a.score.replace('%', ''));
          const scoreB = parseInt(b.score.replace('%', ''));
          return scoreB - scoreA; // Tri décroissant

        case 'name':
          return a.name.localeCompare(b.name);

        case 'date':
        default:
          // Convertir les dates pour le tri
          const dateA = this.parseDate(a.date);
          const dateB = this.parseDate(b.date);
          return dateB.getTime() - dateA.getTime(); // Plus récent en premier
      }
    });
  }

  /**
   * Parse une date au format DD/MM/YYYY
   */
  private parseDate(dateStr: string): Date {
    const [day, month, year] = dateStr.split('/').map(Number);
    return new Date(year, month - 1, day);
  }

  /**
   * Calcule les informations de pagination
   */
  private calculatePagination() {
    this.totalPages = Math.ceil(this.filteredResults.length / this.itemsPerPage);
    this.pages = Array.from({ length: this.totalPages }, (_, i) => i + 1);

    // Ajuster la page courante si nécessaire
    if (this.currentPage > this.totalPages) {
      this.currentPage = Math.max(1, this.totalPages);
    }
  }

  /**
   * Met à jour les résultats paginés
   */
  private updatePaginatedResults() {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedResults = this.filteredResults.slice(startIndex, endIndex);
  }
  // ============================================================================
  // MÉTHODES D'INTERFACE UTILISATEUR
  // ============================================================================

  /**
   * Bascule l'affichage des filtres
   */
  toggleFilters() {
    this.showFilters = !this.showFilters;
  }

  /**
   * Applique un filtre et met à jour l'affichage
   */
  onFilterChange() {
    this.currentPage = 1; // Retour à la première page
    this.applyFiltersAndPagination();
  }

  /**
   * Réinitialise tous les filtres
   */
  resetFilters() {
    this.filters = {
      location: '',
      scoreRange: '',
      competence: '',
      sortBy: 'date'
    };
    this.onFilterChange();
  }

  /**
   * Actualise les résultats
   */
  refreshResults() {
    this.loadTestResults();
  }

  // ============================================================================
  // MÉTHODES DE PAGINATION
  // ============================================================================

  /**
   * Va à une page spécifique
   */
  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePaginatedResults();
    }
  }

  /**
   * Va à la page précédente
   */
  prevPage() {
    if (this.currentPage > 1) {
      this.goToPage(this.currentPage - 1);
    }
  }

  /**
   * Va à la page suivante
   */
  nextPage() {
    if (this.currentPage < this.totalPages) {
      this.goToPage(this.currentPage + 1);
    }
  }

  /**
   * Retourne les pages à afficher dans la pagination
   */
  getVisiblePages(): number[] {
    const maxVisible = 5;
    const half = Math.floor(maxVisible / 2);
    let start = Math.max(1, this.currentPage - half);
    let end = Math.min(this.totalPages, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  }

  // ============================================================================
  // MÉTHODES D'ACTION SUR LES RÉSULTATS
  // ============================================================================

  /**
   * Affiche les détails d'un résultat de test
   */
  viewTestDetails(result: TestResultDisplay) {
    console.log('📋 Affichage des détails pour:', result);
    // TODO: Implémenter la navigation vers la page de détails
    // this.router.navigate(['/protected/test-details', result.id]);
  }

  // ============================================================================
  // MÉTHODES D'ACCEPTATION/REFUS DES CANDIDATS
  // ============================================================================

  /**
   * Vérifie si les boutons d'action doivent être affichés
   */
  shouldShowActionButtons(result: TestResultDisplay): boolean {
    return result.status === 'pending' || result.status === undefined;
  }

  /**
   * Accepte un candidat et envoie un email de notification
   */
  acceptCandidate(result: TestResultDisplay) {
    if (!result.candidateEmail) {
      this.showErrorMessage('Email du candidat non disponible');
      return;
    }

    const confirmMessage = `Êtes-vous sûr de vouloir accepter ${result.name} ?\nUn email de notification sera envoyé automatiquement.`;

    if (confirm(confirmMessage)) {
      console.log('✅ Acceptation du candidat:', result.name);

      this.testResultService.acceptCandidate(result.id, result.candidateEmail).subscribe({
        next: (response) => {
          console.log('✅ Candidat accepté avec succès:', response);

          // Mettre à jour le statut localement
          result.status = 'accepted';

          // Afficher un message de succès
          this.showSuccessMessage(`${result.name} a été accepté(e) ! Un email de notification a été envoyé.`);

          // Optionnel : recharger les données pour s'assurer de la cohérence
          // this.loadTestResults();
        },
        error: (error) => {
          console.error('❌ Erreur lors de l\'acceptation du candidat:', error);
          this.showErrorMessage('Erreur lors de l\'acceptation du candidat. Veuillez réessayer.');
        }
      });
    }
  }

  /**
   * Refuse un candidat
   */
  rejectCandidate(result: TestResultDisplay) {
    if (!result.candidateEmail) {
      this.showErrorMessage('Email du candidat non disponible');
      return;
    }

    const confirmMessage = `Êtes-vous sûr de vouloir refuser ${result.name} ?\nUn email de notification sera envoyé automatiquement.`;

    if (confirm(confirmMessage)) {
      console.log('❌ Refus du candidat:', result.name);

      this.testResultService.rejectCandidate(result.id, result.candidateEmail).subscribe({
        next: (response) => {
          console.log('✅ Candidat refusé avec succès:', response);

          // Mettre à jour le statut localement
          result.status = 'rejected';

          // Afficher un message de succès
          this.showSuccessMessage(`${result.name} a été refusé(e). Un email de notification a été envoyé.`);

          // Optionnel : recharger les données pour s'assurer de la cohérence
          // this.loadTestResults();
        },
        error: (error) => {
          console.error('❌ Erreur lors du refus du candidat:', error);
          this.showErrorMessage('Erreur lors du refus du candidat. Veuillez réessayer.');
        }
      });
    }
  }

  /**
   * Retourne la classe CSS pour le statut
   */
  getStatusClass(status: string | undefined): string {
    switch (status) {
      case 'accepted':
        return 'status-accepted';
      case 'rejected':
        return 'status-rejected';
      case 'pending':
      default:
        return 'status-pending';
    }
  }

  /**
   * Retourne le texte du statut
   */
  getStatusText(status: string | undefined): string {
    switch (status) {
      case 'accepted':
        return 'Accepté';
      case 'rejected':
        return 'Refusé';
      case 'pending':
      default:
        return 'En attente';
    }
  }


  /**
   * Navigue vers le profil du candidat
   */
  viewCandidateProfile(result: TestResultDisplay) {
    console.log('👤 Navigation vers le profil de:', result.name);
    console.log('📊 Données complètes du résultat:', result);

    // Créer un userId de test si aucun n'est disponible (pour les tests)
    let userIdToUse = result.userId;

    if (!userIdToUse) {
      console.warn('⚠️ Aucun userId trouvé, création d\'un ID de test');
      // Générer un ID de test basé sur le nom du candidat
      userIdToUse = 'test-user-' + result.name.toLowerCase().replace(/\s+/g, '-');
      console.log('🔧 ID de test généré:', userIdToUse);
    }

    console.log('✅ ID utilisateur à utiliser:', userIdToUse);

    // Navigation vers le profil du candidat
    console.log('🚀 Tentative de navigation vers:', `/candidate-profile/${userIdToUse}`);

    this.router.navigate(['/candidate-profile', userIdToUse]).then(
      (success) => {
        if (success) {
          console.log('✅ Navigation réussie vers le profil du candidat:', userIdToUse);
          console.log('📍 URL actuelle:', this.router.url);
        } else {
          console.error('❌ Échec de la navigation vers le profil');
          console.error('📍 URL actuelle:', this.router.url);
          this.showErrorMessage('Erreur lors de la navigation vers le profil');
        }
      }
    ).catch((error) => {
      console.error('❌ Erreur de navigation:', error);
      this.showErrorMessage('Erreur lors de la navigation vers le profil');
    });
  }

  /**
   * Supprime un résultat de test
   */
  deleteTestResult(result: TestResultDisplay) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer le résultat de ${result.name} ?`)) {
      // Simulation de suppression pour l'instant
      console.log('🗑️ Suppression du résultat:', result.id);
      this.allResults = this.allResults.filter(r => r.id !== result.id);
      this.applyFiltersAndPagination();
      this.showSuccessMessage(`Résultat de ${result.name} supprimé avec succès`);

      // TODO: Implémenter la vraie suppression via API
      /*
      this.testResultService.deleteTestResult(result.id).subscribe({
        next: () => {
          this.allResults = this.allResults.filter(r => r.id !== result.id);
          this.applyFiltersAndPagination();
          this.showSuccessMessage(`Résultat de ${result.name} supprimé avec succès`);
        },
        error: (error: any) => {
          this.showErrorMessage('Erreur lors de la suppression du résultat');
        }
      });
      */
    }
  }
  // ============================================================================
  // MÉTHODES UTILITAIRES
  // ============================================================================

  /**
   * Retourne la couleur du score selon le pourcentage
   */
  getScoreColor(score: string): string {
    const percentage = parseInt(score.replace('%', ''));
    return this.testResultService.getScoreColor(percentage);
  }

  /**
   * Retourne le statut textuel du score
   */
  getScoreStatus(score: string): string {
    const percentage = parseInt(score.replace('%', ''));
    return this.testResultService.getScoreStatus(percentage);
  }

  /**
   * Retourne la classe CSS pour l'icône de score
   */
  getScoreIcon(score: string): string {
    const percentage = parseInt(score.replace('%', ''));
    if (percentage >= 80) return 'emoji_events'; // Trophée
    if (percentage >= 60) return 'thumb_up'; // Pouce levé
    if (percentage >= 40) return 'trending_up'; // Tendance
    return 'trending_down'; // Tendance baisse
  }

  /**
   * Affiche un message de succès
   */
  private showSuccessMessage(message: string) {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: ['success-snackbar']
    });
  }

  /**
   * Affiche un message d'erreur
   */
  private showErrorMessage(message: string) {
    this.snackBar.open(message, 'Fermer', {
      duration: 5000,
      panelClass: ['error-snackbar']
    });
  }

  /**
   * Affiche un message d'information
   */
  private showInfoMessage(message: string) {
    this.snackBar.open(message, 'Fermer', {
      duration: 4000,
      panelClass: ['info-snackbar']
    });
  }

  /**
   * Retourne le nombre total de résultats
   */
  getTotalResults(): number {
    return this.filteredResults.length;
  }

  /**
   * Retourne les informations de pagination pour l'affichage
   */
  getPaginationInfo(): string {
    if (this.filteredResults.length === 0) {
      return 'Aucun résultat';
    }

    const start = (this.currentPage - 1) * this.itemsPerPage + 1;
    const end = Math.min(this.currentPage * this.itemsPerPage, this.filteredResults.length);
    const total = this.filteredResults.length;

    return `${start}-${end} sur ${total} résultats`;
  }

  /**
   * Fonction de tracking pour ngFor pour optimiser les performances
   */
  trackByResultId(index: number, result: TestResultDisplay): string {
    return result.id;
  }

  /**
   * Parse un string de pourcentage en nombre
   */
  parseInt(value: string): number {
    return parseInt(value.replace('%', ''));
  }
}
