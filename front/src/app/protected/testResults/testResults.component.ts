import { Component, OnInit } from '@angular/core';
import {MatIcon} from '@angular/material/icon';
import {SidebarComponent} from '../sidebar/sidebar.component';
import {<PERSON><PERSON>ard, MatCard<PERSON>ontent, <PERSON><PERSON>ard<PERSON>eader, MatCardTitle} from '@angular/material/card';
import {<PERSON><PERSON><PERSON>on, MatIconButton} from '@angular/material/button';
import {NgForOf, NgIf} from '@angular/common';
import { TestResultService, TestResultDisplay } from '../../services/test-result/test-result.service';

@Component({
  selector: 'app-testResults',
  templateUrl: './testResults.component.html',
  standalone: true,
  imports: [
    MatIcon,
    SidebarComponent,
    MatCard,
    MatCardHeader,
    MatCardContent,
    MatIconButton,
    MatButton,
    NgForOf,
    NgIf,
    MatCardTitle
  ],
  // Modifier le chemin
  styleUrls: ['./testResults.component.css'] // Modifier le chemin
})
export class TestResultsComponent implements OnInit {
  results: TestResultDisplay[] = [];
  loading = true;
  error: string | null = null;

  currentPage = 1;
  pages = [1, 2, 3];

  constructor(private testResultService: TestResultService) {}

  ngOnInit() {
    this.loadTestResults();
  }

  loadTestResults() {
    this.loading = true;
    this.error = null;

    this.testResultService.getAllTestResults().subscribe({
      next: (data) => {
        this.results = this.testResultService.transformToDisplayFormat(data);
        this.loading = false;
        console.log('Résultats chargés:', this.results);
      },
      error: (error) => {
        console.error('Erreur lors du chargement des résultats:', error);
        this.error = 'Erreur lors du chargement des résultats de test';
        this.loading = false;
      }
    });
  }

  getScoreColor(score: string): string {
    const percentage = parseInt(score.replace('%', ''));
    return this.testResultService.getScoreColor(percentage);
  }

  getScoreStatus(score: string): string {
    const percentage = parseInt(score.replace('%', ''));
    return this.testResultService.getScoreStatus(percentage);
  }

  goToPage(page: number) {
    this.currentPage = page;
  }

  prevPage() {
    if (this.currentPage > 1) {
      this.currentPage--;
    }
  }

  nextPage() {
    if (this.currentPage < this.pages.length) {
      this.currentPage++;
    }
  }

  refreshResults() {
    this.loadTestResults();
  }
}
