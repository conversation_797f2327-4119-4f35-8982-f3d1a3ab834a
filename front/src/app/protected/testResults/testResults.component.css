/* ==========================================================================
   Réinitialisation et styles globaux
   ========================================================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Roboto, Segoe UI, sans-serif;
  background-color: #FFFFFF; /* Fond blanc pour la page */
  color: #1C2526; /* Texte sombre pour contraste */
}

/* ==========================================================================
   Conteneur principal de la page
   ========================================================================== */
.results-page {
  display: flex;
  min-height: 100vh;
}

/* ==========================================================================
   Conteneur des résultats
   ========================================================================== */
.results-container {
  margin-left: 250px;
  padding: 24px;
  width: calc(100% - 250px);
  background-color: #FFFFFF; /* Fond blanc pour cohérence */
}
/* ==========================================================================
   États de chargement et d'erreur
   ========================================================================== */
.loading-container, .error-container, .no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin: 20px 0;
}

.loading-container p, .error-container p, .no-results p {
  margin-top: 16px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.no-results {
  grid-column: 1 / -1;
}

.no-results mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: rgba(255, 255, 255, 0.4);
  margin-bottom: 16px;
}

.no-results h3 {
  color: #FFFFFF;
  margin-bottom: 8px;
  font-size: 24px;
}

.error-container mat-icon {
  font-size: 48px;
  width: 48px;
  height: 48px;
  margin-bottom: 16px;
}


/* ==========================================================================
   En-tête
   ========================================================================== */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.results-header h1 {
  font-size: 26px;
  font-weight: 600;
  color: #1C2526; /* Texte sombre pour les titres */
  letter-spacing: -0.02em;
}

.results-header .filter-button {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #F3F2EF; /* Gris clair inspiré du tableau de bord */
  color: #1C2526; /* Texte sombre */
  border-radius: 6px;
  font-size: 14px;
  font-weight: 400;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.results-header .filter-button:hover {
  background-color: #E5E7EB; /* Gris légèrement plus foncé au survol */
  color: #0A66C2; /* Bleu professionnel pour interaction */
}

.results-header .filter-button mat-icon {
  margin-right: 8px;
  font-size: 20px;
  width: 20px;
  height: 20px;
  color: #1C2526; /* Icône sombre */
}

/* ==========================================================================
   Grille des résultats
   ========================================================================== */
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

/* ==========================================================================
   Carte de résultat
   ========================================================================== */
.result-card {
  background-color: #FFFFFF; /* Fond blanc pour les cartes */
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05); /* Ombre douce */
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.result-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1); /* Ombre renforcée au survol */
}

/* ==========================================================================
   En-tête de la carte
   ========================================================================== */
.result-header {
  margin-bottom: 12px;
}

.result-header mat-card-title {
  font-size: 16px;
  font-weight: 500;
  color: #1C2526; /* Texte sombre pour les titres */
}

/* ==========================================================================
   Informations du candidat
   ========================================================================== */
.candidate-info {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.candidate-info .avatar {
  font-size: 36px;
  width: 36px;
  height: 36px;
  color: #FFFFFF; /* Icône blanche */
  background-color: #0A66C2; /* Bleu professionnel pour avatar */
  border-radius: 50%;
  padding: 8px;
  margin-right: 12px;
}

.candidate-info p {
  font-size: 14px;
  color: #1C2526; /* Texte sombre */
  margin: 4px 0;
}

.candidate-info mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 6px;
  vertical-align: middle;
  color: #6B7280; /* Gris moyen pour icônes */
}

/* ==========================================================================
   Informations du test
   ========================================================================== */
.test-info {
  margin-bottom: 16px;
}

.test-info p {
  font-size: 14px;
  color: #1C2526; /* Texte sombre */
  display: flex;
  align-items: center;
  margin: 8px 0;
}

.test-info mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #0A66C2; /* Bleu pour icônes */
}

.test-info .time-limit .red-text {
  color: #EF4444; /* Rouge pour alerte */
}

/* ==========================================================================
   Score
   ========================================================================== */
.score {
  font-size: 15px;
  color: #1C2526; /* Texte sombre */
  font-weight: 500;
}

/* ==========================================================================
   Actions de la carte
   ========================================================================== */
.result-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 12px;
}

.result-actions mat-icon-button {
  color: #6B7280; /* Gris moyen par défaut */
}

.result-actions mat-icon-button[color="primary"]:hover {
  color: #0A66C2; /* Bleu au survol */
}

.result-actions mat-icon-button[color="warn"]:hover {
  color: #EF4444; /* Rouge au survol */
}

.result-actions mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* ==========================================================================
   Pagination
   ========================================================================== */
.pagination-footer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 16px 0;
}

.pagination-footer .pagination-button {
  background-color: #F3F2EF; /* Gris clair pour boutons */
  color: #1C2526; /* Texte sombre */
  border-radius: 6px;
  padding: 8px;
  border: none;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.pagination-footer .pagination-button:hover:not([disabled]) {
  background-color: #E5E7EB; /* Gris légèrement plus foncé au survol */
  color: #0A66C2; /* Bleu pour interaction */
}

.pagination-footer .pagination-button[disabled] {
  background-color: #E5E7EB; /* Gris clair désactivé */
  color: #6B7280; /* Texte gris moyen */
  cursor: not-allowed;
}

.pagination-footer .pagination-button mat-icon {
  font-size: 24px;
  width: 24px;
  height: 24px;
  color: #1C2526; /* Icône sombre */
}

.pagination-footer .pagination-page {
  font-size: 14px;
  color: #1C2526; /* Texte sombre */
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease, color 0.3s ease;
}

.pagination-footer .pagination-page:hover {
  background-color: #E5E7EB; /* Gris clair au survol */
  color: #0A66C2; /* Bleu pour interaction */
}

.pagination-footer .pagination-page.active {
  background-color: #0A66C2; /* Bleu pour page active */
  color: #FFFFFF; /* Blanc pour contraste */
  font-weight: 500;
}

/* ==========================================================================
   Responsive
   ========================================================================== */
@media (max-width: 768px) {
  .results-container {
    margin-left: 70px;
    width: calc(100% - 70px);
    padding: 16px;
  }

  .results-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .results-grid {
    grid-template-columns: 1fr;
  }

  .results-header .filter-button {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 600px) {
  .results-container {
    padding: 12px;
  }

  .results-header h1 {
    font-size: 22px;
  }

  .pagination-footer .pagination-page {
    padding: 6px 10px;
    font-size: 12px;
  }
}
