/* ==========================================================================
   Réinitialisation et styles globaux
   ========================================================================== */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', 'Roboto', 'Segoe UI', sans-serif;
  background: linear-gradient(135deg, #001040FF 0%, #001660FF 100%);
  color: #FFFFFF;
  overflow-x: hidden;
}

/* ==========================================================================
   Conteneur principal de la page
   ========================================================================== */
.results-page {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #001040FF 0%, #001660FF 100%);
  position: relative;
}

.results-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 165, 0, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 165, 0, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

/* ==========================================================================
   Conteneur des résultats
   ========================================================================== */
.results-container {
  margin-left: 250px;
  padding: 32px;
  width: calc(100% - 250px);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.02);
}

/* ==========================================================================
   En-tête
   ========================================================================== */
.results-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.header-left h1 {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  letter-spacing: -0.02em;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #FFFFFF 0%, rgba(255, 165, 0, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.results-count {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.refresh-button, .filter-button {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  background: rgba(255, 165, 0, 0.1);
  color: #FFFFFF;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid rgba(255, 165, 0, 0.3);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.refresh-button:hover, .filter-button:hover {
  background: rgba(255, 165, 0, 0.2);
  border-color: rgba(255, 165, 0, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(255, 165, 0, 0.3);
}

.refresh-button mat-icon, .filter-button mat-icon {
  margin-right: 8px;
  font-size: 20px;
  width: 20px;
  height: 20px;
}

.expand-icon {
  margin-left: 8px !important;
  margin-right: 0 !important;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ==========================================================================
   Panneau de filtres
   ========================================================================== */
.filters-panel {
  max-height: 0;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 24px;
}

.filters-panel.show {
  max-height: 200px;
}

.filters-content {
  padding: 24px;
  background: rgba(255, 255, 255, 0.06);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 16px;
}

.filter-row {
  display: flex;
  gap: 20px;
  align-items: center;
  flex-wrap: wrap;
}

.filter-field {
  min-width: 200px;
  flex: 1;
}

.filter-field ::ng-deep .mat-mdc-form-field-outline {
  color: rgba(255, 255, 255, 0.3) !important;
}

.filter-field ::ng-deep .mat-mdc-form-field-label {
  color: rgba(255, 255, 255, 0.7) !important;
}

.filter-field ::ng-deep .mat-mdc-select-value {
  color: #FFFFFF !important;
}

.reset-filters-button {
  background: rgba(255, 165, 0, 0.1);
  color: #FFFFFF;
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 8px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.reset-filters-button:hover {
  background: rgba(255, 165, 0, 0.2);
  border-color: rgba(255, 165, 0, 0.5);
}

/* ==========================================================================
   États de chargement et d'erreur
   ========================================================================== */
.loading-container, .error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.loading-container p, .error-container p {
  margin-top: 16px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.no-results {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.no-results mat-icon {
  font-size: 64px;
  width: 64px;
  height: 64px;
  color: rgba(255, 255, 255, 0.4);
  margin-bottom: 16px;
}

.no-results h3 {
  color: #FFFFFF;
  margin-bottom: 8px;
}

.no-results p {
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 20px;
}

/* ==========================================================================
   Grille des résultats
   ========================================================================== */
.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

/* ==========================================================================
   Carte de résultat
   ========================================================================== */
.result-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.result-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, rgba(255, 165, 0, 0.8) 0%, rgba(255, 165, 0, 0.4) 100%);
}

.result-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
  border-color: rgba(255, 165, 0, 0.3);
  background: rgba(255, 255, 255, 0.12);
}

/* ==========================================================================
   En-tête de la carte
   ========================================================================== */
.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.result-header mat-card-title {
  font-size: 18px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
  flex: 1;
}

.score-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  color: #FFFFFF;
  font-weight: 600;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.score-badge mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
}

/* ==========================================================================
   Informations du candidat
   ========================================================================== */
.candidate-info {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.candidate-info:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateX(4px);
}

.avatar-container {
  position: relative;
  margin-right: 16px;
}

.avatar {
  font-size: 40px;
  width: 48px;
  height: 48px;
  color: #FFFFFF;
  background: linear-gradient(135deg, #001040FF 0%, rgba(255, 165, 0, 0.8) 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.candidate-details {
  flex: 1;
}

.candidate-name {
  font-size: 16px;
  color: #FFFFFF;
  margin: 0 0 4px 0;
}

.test-date {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.7);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.test-date mat-icon {
  font-size: 16px;
  width: 16px;
  height: 16px;
}

/* ==========================================================================
   Informations du test
   ========================================================================== */
.test-info {
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.test-stat {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 8px;
  border-left: 3px solid rgba(255, 165, 0, 0.5);
}

.test-stat mat-icon {
  font-size: 18px;
  width: 18px;
  height: 18px;
  color: rgba(255, 165, 0, 0.8);
}

.highlight {
  color: rgba(255, 165, 0, 0.9);
  font-weight: 600;
}

/* ==========================================================================
   Section Score
   ========================================================================== */
.score-section {
  margin-bottom: 20px;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.score-main {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 20px;
  font-weight: 700;
}

.score-status {
  font-size: 14px;
  font-weight: 400;
  opacity: 0.8;
}

.score-progress {
  width: 100%;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ==========================================================================
   Actions de la carte
   ========================================================================== */
.result-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.result-actions mat-icon-button {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-actions mat-icon-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.result-actions mat-icon-button[color="primary"] {
  color: rgba(255, 165, 0, 0.8);
}

.result-actions mat-icon-button[color="primary"]:hover {
  background: rgba(255, 165, 0, 0.1);
  border-color: rgba(255, 165, 0, 0.3);
  color: rgba(255, 165, 0, 1);
}

.result-actions mat-icon-button[color="accent"] {
  color: rgba(100, 200, 255, 0.8);
}

.result-actions mat-icon-button[color="accent"]:hover {
  background: rgba(100, 200, 255, 0.1);
  border-color: rgba(100, 200, 255, 0.3);
  color: rgba(100, 200, 255, 1);
}

.result-actions mat-icon-button[color="warn"] {
  color: rgba(255, 100, 100, 0.8);
}

.result-actions mat-icon-button[color="warn"]:hover {
  background: rgba(255, 100, 100, 0.1);
  border-color: rgba(255, 100, 100, 0.3);
  color: rgba(255, 100, 100, 1);
}

.result-actions mat-icon {
  font-size: 20px;
  width: 20px;
  height: 20px;
}

/* ==========================================================================
   Pagination
   ========================================================================== */
.pagination-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 24px;
}

.pagination-info {
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-button {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-button:hover:not([disabled]) {
  background: rgba(255, 165, 0, 0.1);
  border-color: rgba(255, 165, 0, 0.3);
  color: rgba(255, 165, 0, 1);
  transform: translateY(-2px);
}

.pagination-button[disabled] {
  background: rgba(255, 255, 255, 0.02);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  border-color: rgba(255, 255, 255, 0.05);
}

.pagination-pages {
  display: flex;
  gap: 4px;
  margin: 0 12px;
}

.pagination-page {
  min-width: 40px;
  height: 40px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.05);
  color: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-page:hover {
  background: rgba(255, 165, 0, 0.1);
  border-color: rgba(255, 165, 0, 0.3);
  color: rgba(255, 165, 0, 1);
  transform: translateY(-2px);
}

.pagination-page.active {
  background: linear-gradient(135deg, rgba(255, 165, 0, 0.8) 0%, rgba(255, 165, 0, 0.6) 100%);
  border-color: rgba(255, 165, 0, 0.8);
  color: #FFFFFF;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(255, 165, 0, 0.3);
}

.pagination-size {
  min-width: 150px;
}

.items-per-page {
  width: 100%;
}

.items-per-page ::ng-deep .mat-mdc-form-field-outline {
  color: rgba(255, 255, 255, 0.2) !important;
}

.items-per-page ::ng-deep .mat-mdc-form-field-label {
  color: rgba(255, 255, 255, 0.6) !important;
}

.items-per-page ::ng-deep .mat-mdc-select-value {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* ==========================================================================
   Responsive
   ========================================================================== */
@media (max-width: 1200px) {
  .results-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (max-width: 768px) {
  .results-container {
    margin-left: 70px;
    width: calc(100% - 70px);
    padding: 20px;
  }

  .results-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 20px;
  }

  .header-left h1 {
    font-size: 28px;
  }

  .header-actions {
    justify-content: center;
  }

  .results-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .filter-row {
    flex-direction: column;
    gap: 16px;
  }

  .filter-field {
    min-width: 100%;
  }

  .pagination-footer {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .pagination-controls {
    order: 2;
  }

  .pagination-info {
    order: 1;
  }

  .pagination-size {
    order: 3;
    min-width: 200px;
  }
}

@media (max-width: 600px) {
  .results-container {
    margin-left: 0;
    width: 100%;
    padding: 16px;
  }

  .header-left h1 {
    font-size: 24px;
  }

  .result-card {
    padding: 20px;
  }

  .pagination-pages {
    margin: 0 8px;
  }

  .pagination-page, .pagination-button {
    min-width: 36px;
    height: 36px;
    font-size: 12px;
  }

  .test-stat {
    font-size: 13px;
    padding: 6px 10px;
  }

  .score-main {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .results-container {
    padding: 12px;
  }

  .results-header {
    padding: 16px;
  }

  .header-left h1 {
    font-size: 20px;
  }

  .result-card {
    padding: 16px;
  }

  .candidate-info {
    padding: 12px;
  }

  .avatar {
    width: 40px;
    height: 40px;
    font-size: 32px;
  }

  .pagination-footer {
    padding: 16px;
  }

  .pagination-controls {
    gap: 4px;
  }

  .pagination-pages {
    gap: 2px;
    margin: 0 4px;
  }
}

/* ==========================================================================
   Animations d'entrée
   ========================================================================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.result-card {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.result-card:nth-child(1) { animation-delay: 0.1s; }
.result-card:nth-child(2) { animation-delay: 0.2s; }
.result-card:nth-child(3) { animation-delay: 0.3s; }
.result-card:nth-child(4) { animation-delay: 0.4s; }
.result-card:nth-child(5) { animation-delay: 0.5s; }
.result-card:nth-child(6) { animation-delay: 0.6s; }

/* ==========================================================================
   Styles pour les snackbars
   ========================================================================== */
::ng-deep .success-snackbar {
  background: linear-gradient(135deg, #22C55E 0%, #16A34A 100%) !important;
  color: white !important;
}

::ng-deep .error-snackbar {
  background: linear-gradient(135deg, #EF4444 0%, #DC2626 100%) !important;
  color: white !important;
}
