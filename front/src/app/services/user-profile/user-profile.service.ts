import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import {AddExperienceModalComponent} from '../../protected/add-experience-modal/add-experience-modal.component';
import {
  AddCertificationModalComponent
} from '../../protected/add-certification-modal/add-certification-modal.component';

export interface Education {
  school: string;
  degree: string;
  startDate: Date;
  endDate: Date;
  description: string;
}

export interface UserProfile {
  id?: string; // Added id property to match server response
  workspace: any;
  userId: string;
  firstName: string;
  lastName: string;
  company: string;
  email: string;
  photoUrl: string;
  bio: string;
  resumeUrl: string;
  dateOfBirth: Date;
  phoneNumber: string;
  address: string;
  certifications: AddCertificationModalComponent[];
  experiences: AddExperienceModalComponent[];
  educations: Education[];
  skills: string[];
  links: string[];
}

@Injectable({
  providedIn: 'root',
})
export class UserProfileService {
  private apiUrl = `${environment.apiUrl}/user-profiles`;

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {}

  // Obtenir un profil utilisateur par ID
  getProfile(userId: string): Observable<UserProfile> {
    return this.http.get<UserProfile>(`${this.apiUrl}/${userId}`);
  }

  // Créer ou mettre à jour un profil utilisateur
  createOrUpdateProfile(profile: UserProfile): Observable<UserProfile> {
    return this.http.post<UserProfile>(this.apiUrl, profile);
  }

  // Supprimer un profil utilisateur par ID
  deleteProfile(userId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${userId}`);
  }

  // Ajouter une éducation au profil utilisateur
  addEducation(userId: string, education: Education): Observable<UserProfile> {
    return this.http.post<UserProfile>(`${this.apiUrl}/${userId}/add-education`, education);
  }
  addCertification(userId: string, certification: any): Observable<UserProfile> {
    return this.http.post<UserProfile>(`${this.apiUrl}/${userId}/add-certification`, certification);
  }

  // Télécharger le CV
  uploadResume(userId: string, file: File): Observable<string> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('userId', userId);
    return this.http.post(`${this.apiUrl}/upload-cv`, formData, { responseType: 'text' });
  }

  uploadCV(file: File) {

  }

  /**
   * Récupère le profil d'un utilisateur spécifique par son ID
   */
  getUserProfileById(userId: string): Observable<UserProfile> {
    // Vérifier si c'est un utilisateur de test
    if (userId.startsWith('user-') || userId.startsWith('test-user-')) {
      console.log('🔧 Profil de test détecté:', userId);
      return of(this.createTestProfile(userId));
    }

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<UserProfile>(`${this.apiUrl}/users/profile/${userId}`, { headers });
      }),
      catchError((error) => {
        console.error('Erreur lors de la récupération du profil utilisateur:', error);
        // En cas d'erreur, retourner un profil de test
        console.log('🔧 Retour de profil de test en cas d\'erreur');
        return of(this.createTestProfile(userId));
      })
    );
  }

  /**
   * Récupère les informations de base d'un utilisateur par son ID
   */
  getUserById(userId: string): Observable<any> {
    // Vérifier si c'est un utilisateur de test
    if (userId.startsWith('user-') || userId.startsWith('test-user-')) {
      console.log('🔧 Utilisateur de test détecté:', userId);
      return of(this.createTestUser(userId));
    }

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<any>(`${this.apiUrl}/users/${userId}`, { headers });
      }),
      catchError((error) => {
        console.error('Erreur lors de la récupération de l\'utilisateur:', error);
        // En cas d'erreur, retourner des données de test
        console.log('🔧 Retour de données de test en cas d\'erreur');
        return of(this.createTestUser(userId));
      })
    );
  }

  /**
   * Crée un utilisateur de test pour la démonstration
   */
  private createTestUser(userId: string): any {
    const testUsers: { [key: string]: any } = {
      'user-123': {
        id: 'user-123',
        firstName: 'Jean',
        lastName: 'Dupont',
        email: '<EMAIL>',
        phoneNumber: '+33 6 12 34 56 78',
        address: 'Paris, France',
        dateOfBirth: '1990-05-15',
        bio: 'Développeur JavaScript passionné avec 5 ans d\'expérience dans le développement web moderne.'
      },
      'user-456': {
        id: 'user-456',
        firstName: 'Marie',
        lastName: 'Martin',
        email: '<EMAIL>',
        phoneNumber: '+33 6 98 76 54 32',
        address: 'Lyon, France',
        dateOfBirth: '1988-12-03',
        bio: 'Experte React et développeuse full-stack avec une forte expertise en architecture frontend.'
      },
      'user-789': {
        id: 'user-789',
        firstName: 'Pierre',
        lastName: 'Durand',
        email: '<EMAIL>',
        phoneNumber: '+33 6 11 22 33 44',
        address: 'Marseille, France',
        dateOfBirth: '1992-08-20',
        bio: 'Développeur Python spécialisé dans l\'intelligence artificielle et le machine learning.'
      }
    };

    return testUsers[userId] || {
      id: userId,
      firstName: 'Utilisateur',
      lastName: 'Test',
      email: '<EMAIL>',
      phoneNumber: '+33 6 00 00 00 00',
      address: 'France',
      dateOfBirth: '1990-01-01',
      bio: 'Profil de test pour la démonstration.'
    };
  }

  /**
   * Crée un profil de test pour la démonstration
   */
  private createTestProfile(userId: string): UserProfile {
    const testProfiles: { [key: string]: UserProfile } = {
      'user-123': {
        id: 'profile-123',
        userId: 'user-123',
        title: 'Développeur JavaScript Senior',
        photoUrl: null,
        skills: [
          { name: 'JavaScript', level: 'Expert' },
          { name: 'React', level: 'Avancé' },
          { name: 'Node.js', level: 'Avancé' },
          { name: 'TypeScript', level: 'Intermédiaire' }
        ],
        links: [
          { platform: 'GitHub', url: 'https://github.com/jeandupont' },
          { platform: 'LinkedIn', url: 'https://linkedin.com/in/jeandupont' }
        ],
        certifications: [
          { name: 'AWS Certified Developer', issuer: 'Amazon', date: '2023-06-15' }
        ],
        educations: [
          { degree: 'Master Informatique', institution: 'Université Paris-Saclay', year: '2015' }
        ],
        experiences: [
          {
            title: 'Développeur Full-Stack',
            company: 'TechCorp',
            startDate: '2020-01-01',
            endDate: '2024-01-01',
            description: 'Développement d\'applications web modernes avec React et Node.js'
          }
        ],
        cv: null
      },
      'user-456': {
        id: 'profile-456',
        userId: 'user-456',
        title: 'Experte React & Frontend',
        photoUrl: null,
        skills: [
          { name: 'React', level: 'Expert' },
          { name: 'Vue.js', level: 'Avancé' },
          { name: 'CSS/SASS', level: 'Expert' },
          { name: 'JavaScript', level: 'Expert' }
        ],
        links: [
          { platform: 'Portfolio', url: 'https://mariemartin.dev' },
          { platform: 'GitHub', url: 'https://github.com/mariemartin' }
        ],
        certifications: [
          { name: 'React Developer Certification', issuer: 'Meta', date: '2023-03-20' }
        ],
        educations: [
          { degree: 'Ingénieur Informatique', institution: 'INSA Lyon', year: '2012' }
        ],
        experiences: [
          {
            title: 'Lead Frontend Developer',
            company: 'StartupTech',
            startDate: '2018-06-01',
            endDate: null,
            description: 'Direction technique frontend et architecture des applications React'
          }
        ],
        cv: null
      },
      'user-789': {
        id: 'profile-789',
        userId: 'user-789',
        title: 'Développeur Python & IA',
        photoUrl: null,
        skills: [
          { name: 'Python', level: 'Expert' },
          { name: 'Machine Learning', level: 'Avancé' },
          { name: 'TensorFlow', level: 'Avancé' },
          { name: 'Django', level: 'Intermédiaire' }
        ],
        links: [
          { platform: 'Research Gate', url: 'https://researchgate.net/pierredurand' },
          { platform: 'GitHub', url: 'https://github.com/pierredurand' }
        ],
        certifications: [
          { name: 'TensorFlow Developer Certificate', issuer: 'Google', date: '2023-09-10' }
        ],
        educations: [
          { degree: 'Master IA & Data Science', institution: 'Université Aix-Marseille', year: '2016' }
        ],
        experiences: [
          {
            title: 'Data Scientist',
            company: 'AI Solutions',
            startDate: '2019-03-01',
            endDate: null,
            description: 'Développement de modèles de machine learning et solutions IA'
          }
        ],
        cv: null
      }
    };

    return testProfiles[userId] || {
      id: `profile-${userId}`,
      userId: userId,
      title: 'Profil de test',
      photoUrl: null,
      skills: [{ name: 'Test', level: 'Débutant' }],
      links: [],
      certifications: [],
      educations: [],
      experiences: [],
      cv: null
    };
  }
}
