import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import {AddExperienceModalComponent} from '../../protected/add-experience-modal/add-experience-modal.component';
import {
  AddCertificationModalComponent
} from '../../protected/add-certification-modal/add-certification-modal.component';

export interface Education {
  school: string;
  degree: string;
  startDate: Date;
  endDate: Date;
  description: string;
}

export interface UserProfile {
  id?: string; // Added id property to match server response
  workspace: any;
  userId: string;
  firstName: string;
  lastName: string;
  company: string;
  email: string;
  photoUrl?: string | null;
  bio: string;
  resumeUrl: string;
  dateOfBirth: Date;
  phoneNumber: string;
  address: string;
  certifications: AddCertificationModalComponent[];
  experiences: AddExperienceModalComponent[];
  educations: Education[];
  skills: string[];
  links: string[];
}

@Injectable({
  providedIn: 'root',
})
export class UserProfileService {
  private apiUrl = `${environment.apiUrl}/user-profiles`;

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {}

  // Obtenir un profil utilisateur par ID
  getProfile(userId: string): Observable<UserProfile> {
    return this.http.get<UserProfile>(`${this.apiUrl}/${userId}`);
  }

  // Créer ou mettre à jour un profil utilisateur
  createOrUpdateProfile(profile: UserProfile): Observable<UserProfile> {
    return this.http.post<UserProfile>(this.apiUrl, profile);
  }

  // Supprimer un profil utilisateur par ID
  deleteProfile(userId: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${userId}`);
  }

  // Ajouter une éducation au profil utilisateur
  addEducation(userId: string, education: Education): Observable<UserProfile> {
    return this.http.post<UserProfile>(`${this.apiUrl}/${userId}/add-education`, education);
  }
  addCertification(userId: string, certification: any): Observable<UserProfile> {
    return this.http.post<UserProfile>(`${this.apiUrl}/${userId}/add-certification`, certification);
  }

  // Télécharger le CV
  uploadResume(userId: string, file: File): Observable<string> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('userId', userId);
    return this.http.post(`${this.apiUrl}/upload-cv`, formData, { responseType: 'text' });
  }

  uploadCV(file: File) {

  }

  /**
   * Récupère le profil d'un utilisateur spécifique par son ID
   */
  getUserProfileById(userId: string): Observable<UserProfile> {
    console.log('🔄 Récupération du profil utilisateur:', userId);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<UserProfile>(`${environment.apiUrl}/users/profile/${userId}`, { headers });
      }),
      catchError((error) => {
        console.error('❌ Erreur lors de la récupération du profil utilisateur:', error);
        throw error;
      })
    );
  }

  /**
   * Récupère les informations de base d'un utilisateur par son ID
   */
  getUserById(userId: string): Observable<any> {
    console.log('🔄 Récupération des informations utilisateur:', userId);

    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<any>(`${environment.apiUrl}/users/${userId}`, { headers });
      }),
      catchError((error) => {
        console.error('❌ Erreur lors de la récupération de l\'utilisateur:', error);
        throw error;
      })
    );
  }

}
