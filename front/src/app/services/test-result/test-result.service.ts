import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { OidcSecurityService } from 'angular-auth-oidc-client';
import { switchMap } from 'rxjs/operators';

export interface TestResultWithScore {
  id: string;
  testId: string;
  userId: string;
  candidateName: string;
  testName: string;
  submittedAt: Date;
  totalQuestions: number;
  correctAnswers: number;
  scorePercentage: number;
}

export interface TestResultDisplay {
  id: string;
  job: string;
  name: string;
  date: string;
  timeLimit: string;
  timePassed: string;
  score: string;
  correctAnswers: number;
  totalQuestions: number;
}

@Injectable({
  providedIn: 'root'
})
export class TestResultService {
  private apiUrl = `${environment.apiUrl}/tests`;

  constructor(
    private http: HttpClient,
    private oidcSecurityService: OidcSecurityService
  ) {}

  /**
   * Récupère tous les résultats de test avec scores calculés
   */
  getAllTestResults(): Observable<TestResultWithScore[]> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<TestResultWithScore[]>(`${this.apiUrl}/results`, { headers });
      })
    );
  }

  /**
   * Récupère un résultat de test spécifique
   */
  getTestResult(resultId: string): Observable<TestResultWithScore> {
    return this.oidcSecurityService.getAccessToken().pipe(
      switchMap((token) => {
        const headers = new HttpHeaders().set('Authorization', `Bearer ${token}`);
        return this.http.get<TestResultWithScore>(`${this.apiUrl}/results/${resultId}`, { headers });
      })
    );
  }

  /**
   * Transforme les données du backend en format d'affichage
   */
  transformToDisplayFormat(results: TestResultWithScore[]): TestResultDisplay[] {
    return results.map(result => ({
      id: result.id,
      job: result.testName || 'Test technique',
      name: result.candidateName,
      date: this.formatDate(result.submittedAt),
      timeLimit: '1h', // Valeur par défaut, peut être récupérée du test si nécessaire
      timePassed: 'N/A', // Peut être calculé si on stocke le temps de début
      score: `${Math.round(result.scorePercentage)}%`,
      correctAnswers: result.correctAnswers,
      totalQuestions: result.totalQuestions
    }));
  }

  /**
   * Formate une date en format français
   */
  private formatDate(date: Date): string {
    if (!date) return 'N/A';
    
    const d = new Date(date);
    const day = d.getDate().toString().padStart(2, '0');
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const year = d.getFullYear();
    
    return `${day}/${month}/${year}`;
  }

  /**
   * Calcule la couleur du score selon le pourcentage
   */
  getScoreColor(scorePercentage: number): string {
    if (scorePercentage >= 80) return '#22C55E'; // Vert
    if (scorePercentage >= 60) return '#F59E0B'; // Orange
    return '#EF4444'; // Rouge
  }

  /**
   * Retourne le statut textuel du score
   */
  getScoreStatus(scorePercentage: number): string {
    if (scorePercentage >= 80) return 'Excellent';
    if (scorePercentage >= 60) return 'Bien';
    if (scorePercentage >= 40) return 'Moyen';
    return 'Faible';
  }
}
