package org.example.controller.local;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.dto.ProfileDto;
import org.example.dto.UserDto;
import org.example.dto.WorkspaceDto;
import org.example.exceptions.login.AuthenticationException;
import org.example.exceptions.user.UserNotFoundException;
import org.example.model.*;
import org.example.service.WorkspaceProfile.WorkspaceProfileService;
import org.example.service.modelIA.LlamaService;
import org.example.service.pdf.PdfSummaryService;
import org.example.service.user.UserService;
import org.example.service.userProfile.UserProfileService;
import org.example.service.workspace.WorkspaceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/api/users")
public class UserController {

    private final UserService userService;
    private final WorkspaceService workspaceService;
    private final WorkspaceProfileService workspaceProfileService;
    private final UserProfileService userProfileService;
    private final Logger logger = LoggerFactory.getLogger(UserController.class);
    private final PdfSummaryService pdfSummaryService;
    private final LlamaService llamaService;

    public UserController(UserService userService, WorkspaceService workspaceService,
                          WorkspaceProfileService workspaceProfileService, UserProfileService userProfileService,
                          PdfSummaryService pdfSummaryService, LlamaService llamaService) {
        this.userService = userService;
        this.workspaceService = workspaceService;
        this.workspaceProfileService = workspaceProfileService;
        this.userProfileService = userProfileService;
        this.pdfSummaryService = pdfSummaryService;
        this.llamaService = llamaService;
    }

    @GetMapping
    public Page<User> listUsers(Pageable pageable) {
        logger.debug("Listing users");
        return userService.getAllUsers(pageable);
    }

    @GetMapping({"/invitation/{search}", "/invitation/"})
    public List<User> searchUsersForInvitation(@PathVariable(required = false) String search, Membership membership) {
        logger.debug("Searching users for invitation: search={}, workspaceId={}", search, membership.getWorkspaceId());
        return userService.searchForInvitation(search, membership.getWorkspaceId());
    }

    @PostMapping("/create")
    public User createUser(@RequestBody User user) {
        logger.debug("Creating user with email: {}", user.getEmail());
        return userService.createUser(user);
    }

    @PostMapping("/search")
    @PreAuthorize("hasMembershipAuthority('VIEW_USER')")
    public Page<User> searchUsers(@RequestBody User example, Pageable pageable) {
        logger.debug("Searching users with criteria: {}", example);
        return userService.search(example, pageable);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasMembershipAuthority('GET_USER')")
    public User getUserById(@PathVariable String id) {
        logger.debug("Fetching user with ID: {}", id);
        return userService.getUserById(id)
                .orElseThrow(() -> {
                    logger.warn("User with ID {} not found", id);
                    return new UserNotFoundException("Utilisateur avec ID " + id + " non trouvé.");
                });
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasMembershipAuthority('EDIT_USER')")
    public User updateUser(@PathVariable String id, @RequestBody User userDetails) {
        logger.debug("Updating user with ID: {}", id);
        return userService.updateUser(id, userDetails);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasMembershipAuthority('DELETE_USER')")
    public Map<String, Boolean> deleteUser(@PathVariable String id) {
        logger.debug("Deleting user with ID: {}", id);
        userService.deleteUser(id);
        return Map.of("deleted", true);
    }

    @GetMapping("/me")
    public ProfileDto me(Authentication authentication, Membership membership) {
        logger.debug("Fetching profile for user: {}", authentication.getName());
        User user = userService.me();
        UserProfile userProfile = userProfileService.getProfileByUserId(user.getId());

        if (membership == null) {
            return new ProfileDto(new UserDto(user), null, userProfile);
        }
        Workspace workspace = workspaceService.getWorkspaceById(membership.getWorkspaceId()).orElse(null);
        WorkspaceProfile profile = workspaceProfileService.getProfileByWorkspaceId(membership.getWorkspaceId());
        return new ProfileDto(new UserDto(user), new WorkspaceDto(workspace, profile), userProfile);
    }

    @PutMapping("/me/edit")
    public ResponseEntity<?> updateProfile(Authentication authentication, @RequestBody User updatedUser) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("User not authenticated");
        }
        String userId = authentication.getName();
        logger.debug("Received updatedUser for user {}: {}", userId, updatedUser);

        try {
            User existingUser = userService.getUserByEmail(userId)
                    .orElseThrow(() -> {
                        logger.warn("User not found for email: {}", userId);
                        return new UserNotFoundException("User not found for email: " + userId);
                    });

            logger.info("Found existing user for email {} with ID: {}", userId, existingUser.getId());

            // Update user fields
            existingUser.setFirstName(updatedUser.getFirstName());
            existingUser.setLastName(updatedUser.getLastName());
            existingUser.setEmail(updatedUser.getEmail());
            existingUser.setPhoneNumber(updatedUser.getPhoneNumber());
            existingUser.setAddress(updatedUser.getAddress());

            User savedUser = userService.updateUser(existingUser.getId(), existingUser);
            logger.info("User profile updated in database for user {}: {}", userId, savedUser);

            // Verify the update
            User verifiedUser = userService.getUserByEmail(userId)
                    .orElseThrow(() -> {
                        logger.warn("User not found for email after update: {}", userId);
                        return new UserNotFoundException("User not found for email after update: " + userId);
                    });

            if (!verifiedUser.getFirstName().equals(savedUser.getFirstName())) {
                logger.warn("Saved user profile does not match verified user for email {}: Saved={}, Verified={}",
                        userId, savedUser.getFirstName(), verifiedUser.getFirstName());
            }

            return ResponseEntity.ok(savedUser);
        } catch (Exception e) {
            logger.error("Error updating user profile for user {}: {}", userId, e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Error updating profile: " + e.getMessage());
        }
    }

    @PostMapping("/change-password/{email}")
    @PreAuthorize("#email == authentication.name")
    public ResponseEntity<?> changePassword(@PathVariable String email, @RequestBody PasswordChangeRequest request, Authentication authentication) {
        logger.debug("Change password request for user: {}, oldPassword: {}, newPassword: [hidden]",
                email, request.getOldPassword());
        logger.debug("Authentication: name={}, authorities={}", authentication.getName(), authentication.getAuthorities());
        try {
            userService.changePassword(email, request.getOldPassword(), request.getNewPassword());
            logger.info("Password changed successfully for user: {}", email);
            return ResponseEntity.ok().body(Map.of("success", true));
        } catch (UserNotFoundException e) {
            logger.warn("User not found: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(Map.of("error", e.getMessage()));
        } catch (AuthenticationException e) {
            logger.warn("Authentication error: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", e.getMessage()));
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid input: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(Map.of("error", e.getMessage()));
        } catch (Exception e) {
            logger.error("Unexpected error changing password for user {}: {}", email, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(Map.of("error", "Internal server error"));
        }
    }

    public record PasswordChangeRequest(String oldPassword, String newPassword) {
        public String getOldPassword() {
            return oldPassword;
        }

        public String getNewPassword() {
            return newPassword;
        }
    }

    /**
     * Récupère les informations de base d'un utilisateur par son ID
     */
    @GetMapping("/users/{userId}")
    public Mono<Map<String, Object>> getUserById(@PathVariable String userId) {
        return Mono.fromCallable(() -> {
            try {
                Optional<User> userOpt = userRepository.findById(userId);
                if (userOpt.isPresent()) {
                    User user = userOpt.get();
                    Map<String, Object> response = new HashMap<>();
                    response.put("id", user.getId());
                    response.put("firstName", user.getFirstName());
                    response.put("lastName", user.getLastName());
                    response.put("email", user.getEmail());
                    response.put("phoneNumber", user.getPhoneNumber());
                    response.put("address", user.getAddress());
                    response.put("dateOfBirth", user.getDateOfBirth());
                    response.put("bio", user.getBio());

                    System.out.println("✅ Informations utilisateur récupérées pour: " + userId);
                    return response;
                } else {
                    System.err.println("❌ Utilisateur non trouvé: " + userId);
                    throw new RuntimeException("Utilisateur non trouvé");
                }
            } catch (Exception e) {
                System.err.println("❌ Erreur lors de la récupération de l'utilisateur: " + e.getMessage());
                throw new RuntimeException("Erreur lors de la récupération de l'utilisateur");
            }
        });
    }

    /**
     * Récupère le profil complet d'un utilisateur par son ID
     */
    @GetMapping("/users/{userId}/profile")
    public Mono<Map<String, Object>> getUserProfileById(@PathVariable String userId) {
        return Mono.fromCallable(() -> {
            try {
                Optional<UserProfile> profileOpt = userProfileRepository.findByUserId(userId);
                if (profileOpt.isPresent()) {
                    UserProfile profile = profileOpt.get();
                    Map<String, Object> response = new HashMap<>();
                    response.put("id", profile.getId());
                    response.put("userId", profile.getUserId());
                    response.put("title", profile.getTitle());
                    response.put("photoUrl", profile.getPhotoUrl());
                    response.put("skills", profile.getSkills());
                    response.put("links", profile.getLinks());
                    response.put("certifications", profile.getCertifications());
                    response.put("educations", profile.getEducations());
                    response.put("experiences", profile.getExperiences());
                    response.put("cv", profile.getCv());

                    System.out.println("✅ Profil utilisateur récupéré pour: " + userId);
                    return response;
                } else {
                    // Créer un profil vide si aucun profil n'existe
                    Map<String, Object> response = new HashMap<>();
                    response.put("userId", userId);
                    response.put("title", "");
                    response.put("photoUrl", null);
                    response.put("skills", new ArrayList<>());
                    response.put("links", new ArrayList<>());
                    response.put("certifications", new ArrayList<>());
                    response.put("educations", new ArrayList<>());
                    response.put("experiences", new ArrayList<>());
                    response.put("cv", null);

                    System.out.println("ℹ️ Aucun profil trouvé pour l'utilisateur: " + userId + ", retour d'un profil vide");
                    return response;
                }
            } catch (Exception e) {
                System.err.println("❌ Erreur lors de la récupération du profil: " + e.getMessage());
                throw new RuntimeException("Erreur lors de la récupération du profil utilisateur");
            }
        });
    }
}